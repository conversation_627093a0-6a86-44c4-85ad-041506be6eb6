package plugin

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"api-gateway/pkg/telemetry"
)

// CORSPlugin 实现跨域资源共享插件
type CORSPlugin struct {
	logger *telemetry.Logger
	config *CORSConfig
}

// CORSConfig CORS插件配置
type CORSConfig struct {
	Enabled          bool     `json:"enabled" yaml:"enabled"`
	AllowOrigins     []string `json:"allow_origins" yaml:"allow_origins"`
	AllowMethods     []string `json:"allow_methods" yaml:"allow_methods"`
	AllowHeaders     []string `json:"allow_headers" yaml:"allow_headers"`
	ExposeHeaders    []string `json:"expose_headers" yaml:"expose_headers"`
	AllowCredentials bool     `json:"allow_credentials" yaml:"allow_credentials"`
	MaxAge           int      `json:"max_age" yaml:"max_age"` // 预检请求缓存时间（秒）
	AllowAllOrigins  bool     `json:"allow_all_origins" yaml:"allow_all_origins"`
	AllowWildcard    bool     `json:"allow_wildcard" yaml:"allow_wildcard"`
}

// NewCORSPlugin 创建新的CORS插件
func NewCORSPlugin(logger *telemetry.Logger) *CORSPlugin {
	return &CORSPlugin{
		logger: logger.With("plugin", "cors"),
		config: &CORSConfig{
			Enabled: true,
			AllowOrigins: []string{"*"},
			AllowMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"},
			AllowHeaders: []string{
				"Origin", "Content-Type", "Accept", "Authorization",
				"X-Requested-With", "X-Request-ID", "X-API-Key",
			},
			ExposeHeaders:    []string{"X-Request-ID", "X-Response-Time"},
			AllowCredentials: false,
			MaxAge:           86400, // 24小时
			AllowAllOrigins:  false,
			AllowWildcard:    true,
		},
	}
}

func (p *CORSPlugin) Name() string        { return "cors" }
func (p *CORSPlugin) Version() string     { return "1.0.0" }
func (p *CORSPlugin) Description() string { return "跨域资源共享插件，支持配置允许的域名、方法、头部等" }
func (p *CORSPlugin) Priority() int       { return 100 }

func (p *CORSPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth}
}

// Execute 执行CORS处理
func (p *CORSPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	origin := pluginCtx.Headers["Origin"]
	if origin == "" {
		origin = pluginCtx.Headers["origin"]
	}

	// 初始化响应头
	headers := make(map[string]string)

	// 处理预检请求 (OPTIONS)
	if pluginCtx.Method == "OPTIONS" {
		return p.handlePreflightRequest(pluginCtx, origin, headers)
	}

	// 处理实际请求
	return p.handleActualRequest(pluginCtx, origin, headers)
}

// handlePreflightRequest 处理预检请求
func (p *CORSPlugin) handlePreflightRequest(pluginCtx *PluginContext, origin string, headers map[string]string) (*PluginResult, error) {
	// 检查Origin是否被允许
	if !p.isOriginAllowed(origin) {
		p.logger.Warn("CORS预检请求被拒绝：Origin不被允许",
			"request_id", pluginCtx.RequestID,
			"origin", origin,
			"path", pluginCtx.Path)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 403,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(`{"error":"CORS预检请求被拒绝：Origin不被允许","code":403}`),
		}, nil
	}

	// 设置CORS响应头
	p.setCORSHeaders(headers, origin)

	// 检查请求方法
	requestMethod := pluginCtx.Headers["Access-Control-Request-Method"]
	if requestMethod == "" {
		requestMethod = pluginCtx.Headers["access-control-request-method"]
	}
	
	if requestMethod != "" && !p.isMethodAllowed(requestMethod) {
		p.logger.Warn("CORS预检请求被拒绝：方法不被允许",
			"request_id", pluginCtx.RequestID,
			"origin", origin,
			"method", requestMethod)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 405,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(`{"error":"CORS预检请求被拒绝：方法不被允许","code":405}`),
		}, nil
	}

	// 检查请求头
	requestHeaders := pluginCtx.Headers["Access-Control-Request-Headers"]
	if requestHeaders == "" {
		requestHeaders = pluginCtx.Headers["access-control-request-headers"]
	}
	
	if requestHeaders != "" && !p.areHeadersAllowed(requestHeaders) {
		p.logger.Warn("CORS预检请求被拒绝：请求头不被允许",
			"request_id", pluginCtx.RequestID,
			"origin", origin,
			"headers", requestHeaders)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 403,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(`{"error":"CORS预检请求被拒绝：请求头不被允许","code":403}`),
		}, nil
	}

	// 设置预检响应头
	headers["Access-Control-Allow-Methods"] = strings.Join(p.config.AllowMethods, ", ")
	headers["Access-Control-Allow-Headers"] = strings.Join(p.config.AllowHeaders, ", ")
	headers["Access-Control-Max-Age"] = strconv.Itoa(p.config.MaxAge)

	p.logger.Debug("CORS预检请求已处理",
		"request_id", pluginCtx.RequestID,
		"origin", origin,
		"method", requestMethod)

	return &PluginResult{
		Continue:   false,
		StatusCode: 204,
		Headers:    headers,
		Body:       []byte{},
	}, nil
}

// handleActualRequest 处理实际请求
func (p *CORSPlugin) handleActualRequest(pluginCtx *PluginContext, origin string, headers map[string]string) (*PluginResult, error) {
	// 检查Origin是否被允许
	if origin != "" && !p.isOriginAllowed(origin) {
		p.logger.Warn("CORS请求被拒绝：Origin不被允许",
			"request_id", pluginCtx.RequestID,
			"origin", origin,
			"path", pluginCtx.Path)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 403,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: []byte(`{"error":"CORS请求被拒绝：Origin不被允许","code":403}`),
		}, nil
	}

	// 设置CORS响应头
	if origin != "" {
		p.setCORSHeaders(headers, origin)
	}

	// 将CORS头添加到插件上下文中，以便在响应中包含
	if pluginCtx.Headers == nil {
		pluginCtx.Headers = make(map[string]string)
	}
	
	for key, value := range headers {
		pluginCtx.Headers[key] = value
	}

	p.logger.Debug("CORS请求已处理",
		"request_id", pluginCtx.RequestID,
		"origin", origin,
		"method", pluginCtx.Method)

	return &PluginResult{Continue: true}, nil
}

// setCORSHeaders 设置CORS响应头
func (p *CORSPlugin) setCORSHeaders(headers map[string]string, origin string) {
	// 设置允许的Origin
	if p.config.AllowAllOrigins || (len(p.config.AllowOrigins) == 1 && p.config.AllowOrigins[0] == "*") {
		headers["Access-Control-Allow-Origin"] = "*"
	} else if origin != "" {
		headers["Access-Control-Allow-Origin"] = origin
	}

	// 设置是否允许凭据
	if p.config.AllowCredentials {
		headers["Access-Control-Allow-Credentials"] = "true"
		// 如果允许凭据，不能使用通配符
		if headers["Access-Control-Allow-Origin"] == "*" {
			headers["Access-Control-Allow-Origin"] = origin
		}
	}

	// 设置暴露的响应头
	if len(p.config.ExposeHeaders) > 0 {
		headers["Access-Control-Expose-Headers"] = strings.Join(p.config.ExposeHeaders, ", ")
	}

	// 设置Vary头以支持缓存
	headers["Vary"] = "Origin"
}

// isOriginAllowed 检查Origin是否被允许
func (p *CORSPlugin) isOriginAllowed(origin string) bool {
	if origin == "" {
		return true // 同源请求
	}

	if p.config.AllowAllOrigins {
		return true
	}

	for _, allowedOrigin := range p.config.AllowOrigins {
		if allowedOrigin == "*" {
			return true
		}
		
		if allowedOrigin == origin {
			return true
		}
		
		// 支持通配符匹配
		if p.config.AllowWildcard && p.matchOriginWithWildcard(allowedOrigin, origin) {
			return true
		}
	}

	return false
}

// matchOriginWithWildcard 使用通配符匹配Origin
func (p *CORSPlugin) matchOriginWithWildcard(pattern, origin string) bool {
	if !strings.Contains(pattern, "*") {
		return pattern == origin
	}

	// 将通配符转换为正则表达式
	regexPattern := strings.ReplaceAll(regexp.QuoteMeta(pattern), "\\*", ".*")
	matched, err := regexp.MatchString("^"+regexPattern+"$", origin)
	if err != nil {
		p.logger.Error("Origin通配符匹配失败", "pattern", pattern, "origin", origin, "error", err)
		return false
	}
	
	return matched
}

// isMethodAllowed 检查方法是否被允许
func (p *CORSPlugin) isMethodAllowed(method string) bool {
	for _, allowedMethod := range p.config.AllowMethods {
		if allowedMethod == method || allowedMethod == "*" {
			return true
		}
	}
	return false
}

// areHeadersAllowed 检查请求头是否被允许
func (p *CORSPlugin) areHeadersAllowed(requestHeaders string) bool {
	headers := strings.Split(requestHeaders, ",")
	for _, header := range headers {
		header = strings.TrimSpace(header)
		if !p.isHeaderAllowed(header) {
			return false
		}
	}
	return true
}

// isHeaderAllowed 检查单个请求头是否被允许
func (p *CORSPlugin) isHeaderAllowed(header string) bool {
	header = strings.ToLower(strings.TrimSpace(header))
	
	for _, allowedHeader := range p.config.AllowHeaders {
		if strings.ToLower(allowedHeader) == header || allowedHeader == "*" {
			return true
		}
	}
	
	// 简单请求头总是被允许
	simpleHeaders := []string{
		"accept", "accept-language", "content-language", "content-type",
	}
	
	for _, simpleHeader := range simpleHeaders {
		if header == simpleHeader {
			return true
		}
	}
	
	return false
}

// Configure 配置插件
func (p *CORSPlugin) Configure(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}

	if allowOrigins, ok := config["allow_origins"].([]interface{}); ok {
		p.config.AllowOrigins = make([]string, 0, len(allowOrigins))
		for _, origin := range allowOrigins {
			if originStr, ok := origin.(string); ok {
				p.config.AllowOrigins = append(p.config.AllowOrigins, originStr)
			}
		}
	}

	if allowMethods, ok := config["allow_methods"].([]interface{}); ok {
		p.config.AllowMethods = make([]string, 0, len(allowMethods))
		for _, method := range allowMethods {
			if methodStr, ok := method.(string); ok {
				p.config.AllowMethods = append(p.config.AllowMethods, methodStr)
			}
		}
	}

	if allowHeaders, ok := config["allow_headers"].([]interface{}); ok {
		p.config.AllowHeaders = make([]string, 0, len(allowHeaders))
		for _, header := range allowHeaders {
			if headerStr, ok := header.(string); ok {
				p.config.AllowHeaders = append(p.config.AllowHeaders, headerStr)
			}
		}
	}

	if exposeHeaders, ok := config["expose_headers"].([]interface{}); ok {
		p.config.ExposeHeaders = make([]string, 0, len(exposeHeaders))
		for _, header := range exposeHeaders {
			if headerStr, ok := header.(string); ok {
				p.config.ExposeHeaders = append(p.config.ExposeHeaders, headerStr)
			}
		}
	}

	if allowCredentials, ok := config["allow_credentials"].(bool); ok {
		p.config.AllowCredentials = allowCredentials
	}

	if maxAge, ok := config["max_age"].(int); ok {
		p.config.MaxAge = maxAge
	}

	if allowAllOrigins, ok := config["allow_all_origins"].(bool); ok {
		p.config.AllowAllOrigins = allowAllOrigins
	}

	if allowWildcard, ok := config["allow_wildcard"].(bool); ok {
		p.config.AllowWildcard = allowWildcard
	}

	p.logger.Info("CORS插件配置已更新",
		"enabled", p.config.Enabled,
		"allow_origins", len(p.config.AllowOrigins),
		"allow_methods", len(p.config.AllowMethods),
		"allow_credentials", p.config.AllowCredentials)

	return nil
}

func (p *CORSPlugin) Start() error {
	p.logger.Info("CORS插件已启动")
	return nil
}

func (p *CORSPlugin) Stop() error {
	p.logger.Info("CORS插件已停止")
	return nil
}

func (p *CORSPlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("CORS插件未启用")
	}
	return nil
}
