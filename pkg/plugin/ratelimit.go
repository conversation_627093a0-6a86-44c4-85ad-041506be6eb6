package plugin

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"

	"golang.org/x/time/rate"
)

// RateLimitPlugin 实现基于令牌桶算法的限流插件
type RateLimitPlugin struct {
	logger  *telemetry.Logger
	config  *RateLimitConfig
	limiters map[string]*rate.Limiter
	mu      sync.RWMutex
}

// RateLimitConfig 限流插件配置
type RateLimitConfig struct {
	Enabled     bool                    `json:"enabled" yaml:"enabled"`
	Algorithm   string                  `json:"algorithm" yaml:"algorithm"` // token_bucket, sliding_window
	DefaultRate int                     `json:"default_rate" yaml:"default_rate"`
	DefaultBurst int                    `json:"default_burst" yaml:"default_burst"`
	KeyType     string                  `json:"key_type" yaml:"key_type"` // ip, user, path, custom
	Rules       []RateLimitRule         `json:"rules" yaml:"rules"`
	Storage     string                  `json:"storage" yaml:"storage"` // memory, redis
	TTL         time.Duration           `json:"ttl" yaml:"ttl"`
}

// RateLimitRule 限流规则
type RateLimitRule struct {
	Name        string        `json:"name" yaml:"name"`
	Path        string        `json:"path" yaml:"path"`
	Method      string        `json:"method" yaml:"method"`
	Rate        int           `json:"rate" yaml:"rate"`
	Burst       int           `json:"burst" yaml:"burst"`
	KeyType     string        `json:"key_type" yaml:"key_type"`
	TTL         time.Duration `json:"ttl" yaml:"ttl"`
	Enabled     bool          `json:"enabled" yaml:"enabled"`
}

// NewRateLimitPlugin 创建新的限流插件
func NewRateLimitPlugin(logger *telemetry.Logger) *RateLimitPlugin {
	return &RateLimitPlugin{
		logger:   logger.With("plugin", "rate_limit"),
		limiters: make(map[string]*rate.Limiter),
		config: &RateLimitConfig{
			Enabled:     true,
			Algorithm:   "token_bucket",
			DefaultRate: 100,
			DefaultBurst: 200,
			KeyType:     "ip",
			Storage:     "memory",
			TTL:         time.Hour,
		},
	}
}

func (p *RateLimitPlugin) Name() string        { return "rate_limit" }
func (p *RateLimitPlugin) Version() string     { return "1.0.0" }
func (p *RateLimitPlugin) Description() string { return "基于令牌桶算法的限流插件，支持多维度限流控制" }
func (p *RateLimitPlugin) Priority() int       { return 200 }

func (p *RateLimitPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth}
}

// Execute 执行限流检查
func (p *RateLimitPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if !p.config.Enabled {
		return &PluginResult{Continue: true}, nil
	}

	// 生成限流键
	key := p.generateKey(pluginCtx)
	if key == "" {
		p.logger.Warn("无法生成限流键", "request_id", pluginCtx.RequestID)
		return &PluginResult{Continue: true}, nil
	}

	// 获取适用的限流规则
	rule := p.getApplicableRule(pluginCtx)
	
	// 获取或创建限流器
	limiter := p.getLimiter(key, rule)
	
	// 检查是否允许请求
	if !limiter.Allow() {
		p.logger.Warn("请求被限流",
			"request_id", pluginCtx.RequestID,
			"key", key,
			"path", pluginCtx.Path,
			"client_ip", pluginCtx.ClientIP)
		
		return &PluginResult{
			Continue:   false,
			StatusCode: 429,
			Headers: map[string]string{
				"Content-Type":           "application/json",
				"X-RateLimit-Limit":      strconv.Itoa(rule.Rate),
				"X-RateLimit-Remaining":  "0",
				"X-RateLimit-Reset":      strconv.FormatInt(time.Now().Add(time.Second).Unix(), 10),
				"Retry-After":            "1",
			},
			Body: []byte(`{"error":"请求频率过高，请稍后重试","code":429}`),
		}, nil
	}

	// 添加限流信息到响应头
	if pluginCtx.Headers == nil {
		pluginCtx.Headers = make(map[string]string)
	}
	
	pluginCtx.Headers["X-RateLimit-Limit"] = strconv.Itoa(rule.Rate)
	pluginCtx.Headers["X-RateLimit-Remaining"] = strconv.Itoa(int(limiter.Tokens()))

	return &PluginResult{Continue: true}, nil
}

// generateKey 生成限流键
func (p *RateLimitPlugin) generateKey(pluginCtx *PluginContext) string {
	switch p.config.KeyType {
	case "ip":
		return fmt.Sprintf("rate_limit:ip:%s", pluginCtx.ClientIP)
	case "user":
		if pluginCtx.UserID != "" {
			return fmt.Sprintf("rate_limit:user:%s", pluginCtx.UserID)
		}
		// 如果没有用户ID，回退到IP
		return fmt.Sprintf("rate_limit:ip:%s", pluginCtx.ClientIP)
	case "path":
		return fmt.Sprintf("rate_limit:path:%s:%s", pluginCtx.Method, pluginCtx.Path)
	case "custom":
		// 组合键：IP + 路径
		return fmt.Sprintf("rate_limit:custom:%s:%s:%s", pluginCtx.ClientIP, pluginCtx.Method, pluginCtx.Path)
	default:
		return fmt.Sprintf("rate_limit:ip:%s", pluginCtx.ClientIP)
	}
}

// getApplicableRule 获取适用的限流规则
func (p *RateLimitPlugin) getApplicableRule(pluginCtx *PluginContext) RateLimitRule {
	// 检查是否有匹配的规则
	for _, rule := range p.config.Rules {
		if !rule.Enabled {
			continue
		}
		
		// 检查路径匹配
		if rule.Path != "" && !p.matchPath(rule.Path, pluginCtx.Path) {
			continue
		}
		
		// 检查方法匹配
		if rule.Method != "" && rule.Method != "*" && rule.Method != pluginCtx.Method {
			continue
		}
		
		return rule
	}
	
	// 返回默认规则
	return RateLimitRule{
		Name:    "default",
		Rate:    p.config.DefaultRate,
		Burst:   p.config.DefaultBurst,
		KeyType: p.config.KeyType,
		TTL:     p.config.TTL,
		Enabled: true,
	}
}

// matchPath 检查路径是否匹配
func (p *RateLimitPlugin) matchPath(pattern, path string) bool {
	// 简单的通配符匹配
	if strings.Contains(pattern, "*") {
		pattern = strings.ReplaceAll(pattern, "*", ".*")
		matched, _ := regexp.MatchString("^"+pattern+"$", path)
		return matched
	}
	return pattern == path
}

// getLimiter 获取或创建限流器
func (p *RateLimitPlugin) getLimiter(key string, rule RateLimitRule) *rate.Limiter {
	p.mu.RLock()
	limiter, exists := p.limiters[key]
	p.mu.RUnlock()
	
	if exists {
		return limiter
	}
	
	p.mu.Lock()
	defer p.mu.Unlock()
	
	// 双重检查
	if limiter, exists := p.limiters[key]; exists {
		return limiter
	}
	
	// 创建新的限流器
	limiter = rate.NewLimiter(rate.Limit(rule.Rate), rule.Burst)
	p.limiters[key] = limiter
	
	// 启动清理协程（简化版本，实际应该使用更高效的清理机制）
	go p.cleanupLimiter(key, rule.TTL)
	
	return limiter
}

// cleanupLimiter 清理过期的限流器
func (p *RateLimitPlugin) cleanupLimiter(key string, ttl time.Duration) {
	time.Sleep(ttl)
	p.mu.Lock()
	delete(p.limiters, key)
	p.mu.Unlock()
}

// Configure 配置插件
func (p *RateLimitPlugin) Configure(config map[string]interface{}) error {
	if !p.config.Enabled {
		return nil
	}
	
	// 解析配置
	if enabled, ok := config["enabled"].(bool); ok {
		p.config.Enabled = enabled
	}
	
	if algorithm, ok := config["algorithm"].(string); ok {
		p.config.Algorithm = algorithm
	}
	
	if defaultRate, ok := config["default_rate"].(int); ok {
		p.config.DefaultRate = defaultRate
	}
	
	if defaultBurst, ok := config["default_burst"].(int); ok {
		p.config.DefaultBurst = defaultBurst
	}
	
	if keyType, ok := config["key_type"].(string); ok {
		p.config.KeyType = keyType
	}
	
	// 解析规则
	if rules, ok := config["rules"].([]interface{}); ok {
		p.config.Rules = make([]RateLimitRule, 0, len(rules))
		for _, ruleData := range rules {
			if ruleMap, ok := ruleData.(map[string]interface{}); ok {
				rule := RateLimitRule{
					Enabled: true,
				}
				
				if name, ok := ruleMap["name"].(string); ok {
					rule.Name = name
				}
				if path, ok := ruleMap["path"].(string); ok {
					rule.Path = path
				}
				if method, ok := ruleMap["method"].(string); ok {
					rule.Method = method
				}
				if rate, ok := ruleMap["rate"].(int); ok {
					rule.Rate = rate
				}
				if burst, ok := ruleMap["burst"].(int); ok {
					rule.Burst = burst
				}
				if keyType, ok := ruleMap["key_type"].(string); ok {
					rule.KeyType = keyType
				}
				if enabled, ok := ruleMap["enabled"].(bool); ok {
					rule.Enabled = enabled
				}
				
				p.config.Rules = append(p.config.Rules, rule)
			}
		}
	}
	
	p.logger.Info("限流插件配置已更新",
		"enabled", p.config.Enabled,
		"algorithm", p.config.Algorithm,
		"default_rate", p.config.DefaultRate,
		"rules_count", len(p.config.Rules))
	
	return nil
}

func (p *RateLimitPlugin) Start() error {
	p.logger.Info("限流插件已启动")
	return nil
}

func (p *RateLimitPlugin) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	// 清理所有限流器
	p.limiters = make(map[string]*rate.Limiter)
	p.logger.Info("限流插件已停止")
	return nil
}

func (p *RateLimitPlugin) HealthCheck() error {
	if !p.config.Enabled {
		return fmt.Errorf("限流插件未启用")
	}
	return nil
}
