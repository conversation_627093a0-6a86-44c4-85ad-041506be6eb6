package config

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"
)

// Logger 定义日志接口，避免循环导入
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	With(key string, value interface{}) Logger
}

// DynamicConfigManager manages dynamic configuration updates
type DynamicConfigManager struct {
	config    *Config
	logger    Logger
	mu        sync.RWMutex
	
	// Configuration storage
	storage   ConfigStorage
	
	// Change listeners
	listeners map[string][]ConfigChangeListener
	listenerMu sync.RWMutex
	
	// Version management
	currentVersion string
	versionHistory []ConfigVersion
	
	// Update channels
	updateChan chan ConfigUpdate
	stopChan   chan struct{}
	
	// Configuration validation
	validator ConfigValidator
}

// ConfigStorage interface for configuration persistence
type ConfigStorage interface {
	Get(key string) ([]byte, error)
	Set(key string, value []byte) error
	Delete(key string) error
	List(prefix string) (map[string][]byte, error)
	Watch(key string) (<-chan ConfigEvent, error)
	Close() error
}

// ConfigChangeListener handles configuration changes
type ConfigChangeListener interface {
	OnConfigChange(ctx context.Context, change ConfigChange) error
	GetName() string
}

// ConfigValidator validates configuration changes
type ConfigValidator interface {
	ValidateConfig(config *Config) error
	ValidateRouteConfig(route RouteConfig) error
	ValidateSecurityConfig(security SecurityConfig) error
	ValidatePluginConfig(plugin PluginConfig) error
}

// ConfigUpdate represents a configuration update request
type ConfigUpdate struct {
	Type      ConfigUpdateType `json:"type"`
	Section   string           `json:"section"`
	Key       string           `json:"key"`
	Value     interface{}      `json:"value"`
	Version   string           `json:"version"`
	Timestamp time.Time        `json:"timestamp"`
	Source    string           `json:"source"`
}

// ConfigUpdateType defines the type of configuration update
type ConfigUpdateType string

const (
	ConfigUpdateTypeAdd    ConfigUpdateType = "add"
	ConfigUpdateTypeUpdate ConfigUpdateType = "update"
	ConfigUpdateTypeDelete ConfigUpdateType = "delete"
	ConfigUpdateTypeReload ConfigUpdateType = "reload"
)

// ConfigChange represents a configuration change event
type ConfigChange struct {
	Type      ConfigUpdateType `json:"type"`
	Section   string           `json:"section"`
	Key       string           `json:"key"`
	OldValue  interface{}      `json:"old_value"`
	NewValue  interface{}      `json:"new_value"`
	Version   string           `json:"version"`
	Timestamp time.Time        `json:"timestamp"`
}

// ConfigEvent represents a configuration storage event
type ConfigEvent struct {
	Type  string      `json:"type"`
	Key   string      `json:"key"`
	Value []byte      `json:"value"`
	Error error       `json:"error"`
}

// ConfigVersion represents a configuration version
type ConfigVersion struct {
	Version   string    `json:"version"`
	Config    *Config   `json:"config"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source"`
	Comment   string    `json:"comment"`
}

// NewDynamicConfigManager creates a new dynamic configuration manager
func NewDynamicConfigManager(config *Config, storage ConfigStorage, logger Logger) *DynamicConfigManager {
	return &DynamicConfigManager{
		config:         config,
		storage:        storage,
		logger:         logger,
		listeners:      make(map[string][]ConfigChangeListener),
		currentVersion: generateVersion(),
		versionHistory: make([]ConfigVersion, 0),
		updateChan:     make(chan ConfigUpdate, 100),
		stopChan:       make(chan struct{}),
		validator:      NewDefaultConfigValidator(),
	}
}

// Start starts the dynamic configuration manager
func (dcm *DynamicConfigManager) Start(ctx context.Context) error {
	dcm.logger.Info("Starting dynamic configuration manager")
	
	// Save initial configuration version
	if err := dcm.saveConfigVersion("initial", "system"); err != nil {
		return fmt.Errorf("failed to save initial config version: %w", err)
	}
	
	// Start update processor
	go dcm.processUpdates(ctx)
	
	// Start configuration watcher
	go dcm.watchConfigChanges(ctx)
	
	return nil
}

// Stop stops the dynamic configuration manager
func (dcm *DynamicConfigManager) Stop() error {
	dcm.logger.Info("Stopping dynamic configuration manager")
	
	close(dcm.stopChan)
	
	if dcm.storage != nil {
		return dcm.storage.Close()
	}
	
	return nil
}

// GetConfig returns the current configuration
func (dcm *DynamicConfigManager) GetConfig() *Config {
	dcm.mu.RLock()
	defer dcm.mu.RUnlock()
	
	// Return a deep copy to prevent external modifications
	return dcm.deepCopyConfig(dcm.config)
}

// UpdateConfig updates the configuration
func (dcm *DynamicConfigManager) UpdateConfig(update ConfigUpdate) error {
	dcm.logger.Info("Received configuration update", 
		"type", update.Type, 
		"section", update.Section, 
		"key", update.Key)
	
	// Validate update
	if err := dcm.validateUpdate(update); err != nil {
		return fmt.Errorf("invalid configuration update: %w", err)
	}
	
	// Send update to processing channel
	select {
	case dcm.updateChan <- update:
		return nil
	default:
		return fmt.Errorf("update channel is full")
	}
}

// AddListener adds a configuration change listener
func (dcm *DynamicConfigManager) AddListener(section string, listener ConfigChangeListener) {
	dcm.listenerMu.Lock()
	defer dcm.listenerMu.Unlock()
	
	if dcm.listeners[section] == nil {
		dcm.listeners[section] = make([]ConfigChangeListener, 0)
	}
	
	dcm.listeners[section] = append(dcm.listeners[section], listener)
	
	dcm.logger.Info("Added configuration listener", 
		"section", section, 
		"listener", listener.GetName())
}

// RemoveListener removes a configuration change listener
func (dcm *DynamicConfigManager) RemoveListener(section string, listenerName string) {
	dcm.listenerMu.Lock()
	defer dcm.listenerMu.Unlock()
	
	listeners := dcm.listeners[section]
	for i, listener := range listeners {
		if listener.GetName() == listenerName {
			dcm.listeners[section] = append(listeners[:i], listeners[i+1:]...)
			dcm.logger.Info("Removed configuration listener", 
				"section", section, 
				"listener", listenerName)
			break
		}
	}
}

// GetVersionHistory returns the configuration version history
func (dcm *DynamicConfigManager) GetVersionHistory() []ConfigVersion {
	dcm.mu.RLock()
	defer dcm.mu.RUnlock()
	
	// Return a copy
	history := make([]ConfigVersion, len(dcm.versionHistory))
	copy(history, dcm.versionHistory)
	return history
}

// RollbackToVersion rolls back to a specific configuration version
func (dcm *DynamicConfigManager) RollbackToVersion(version string) error {
	dcm.mu.Lock()
	defer dcm.mu.Unlock()
	
	// Find the version
	var targetVersion *ConfigVersion
	for _, v := range dcm.versionHistory {
		if v.Version == version {
			targetVersion = &v
			break
		}
	}
	
	if targetVersion == nil {
		return fmt.Errorf("version %s not found", version)
	}
	
	// Validate the target configuration
	if err := dcm.validator.ValidateConfig(targetVersion.Config); err != nil {
		return fmt.Errorf("target configuration is invalid: %w", err)
	}
	
	// Update configuration
	oldConfig := dcm.config
	dcm.config = dcm.deepCopyConfig(targetVersion.Config)
	dcm.currentVersion = generateVersion()
	
	// Save rollback as new version
	if err := dcm.saveConfigVersion(fmt.Sprintf("rollback to %s", version), "system"); err != nil {
		dcm.logger.Error("Failed to save rollback version", "error", err)
	}
	
	// Notify listeners
	change := ConfigChange{
		Type:      ConfigUpdateTypeUpdate,
		Section:   "all",
		Key:       "config",
		OldValue:  oldConfig,
		NewValue:  dcm.config,
		Version:   dcm.currentVersion,
		Timestamp: time.Now(),
	}
	
	dcm.notifyListeners(context.Background(), change)
	
	dcm.logger.Info("Configuration rolled back", 
		"from_version", dcm.currentVersion, 
		"to_version", version)
	
	return nil
}

// processUpdates processes configuration updates
func (dcm *DynamicConfigManager) processUpdates(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-dcm.stopChan:
			return
		case update := <-dcm.updateChan:
			if err := dcm.applyUpdate(ctx, update); err != nil {
				dcm.logger.Error("Failed to apply configuration update", 
					"error", err, 
					"update", update)
			}
		}
	}
}

// watchConfigChanges watches for external configuration changes
func (dcm *DynamicConfigManager) watchConfigChanges(ctx context.Context) {
	// This would watch for changes from external sources like config centers
	// Implementation depends on the specific storage backend
}

// applyUpdate applies a configuration update
func (dcm *DynamicConfigManager) applyUpdate(ctx context.Context, update ConfigUpdate) error {
	dcm.mu.Lock()
	defer dcm.mu.Unlock()

	oldConfig := dcm.deepCopyConfig(dcm.config)

	// Apply the update based on type and section
	switch update.Section {
	case "routes":
		if err := dcm.updateRoutes(update); err != nil {
			return err
		}
	case "security":
		if err := dcm.updateSecurity(update); err != nil {
			return err
		}
	case "plugins":
		if err := dcm.updatePlugins(update); err != nil {
			return err
		}
	case "auth":
		if err := dcm.updateAuth(update); err != nil {
			return err
		}
	default:
		return fmt.Errorf("unsupported configuration section: %s", update.Section)
	}

	// Validate the updated configuration
	if err := dcm.validator.ValidateConfig(dcm.config); err != nil {
		// Rollback on validation failure
		dcm.config = oldConfig
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	// Save new version
	dcm.currentVersion = generateVersion()
	if err := dcm.saveConfigVersion(fmt.Sprintf("update %s.%s", update.Section, update.Key), update.Source); err != nil {
		dcm.logger.Error("Failed to save config version", "error", err)
	}

	// Persist to storage
	if err := dcm.persistConfig(); err != nil {
		dcm.logger.Error("Failed to persist configuration", "error", err)
	}

	// Notify listeners
	change := ConfigChange{
		Type:      update.Type,
		Section:   update.Section,
		Key:       update.Key,
		OldValue:  dcm.getConfigValue(oldConfig, update.Section, update.Key),
		NewValue:  update.Value,
		Version:   dcm.currentVersion,
		Timestamp: time.Now(),
	}

	dcm.notifyListeners(ctx, change)

	dcm.logger.Info("Configuration update applied",
		"section", update.Section,
		"key", update.Key,
		"version", dcm.currentVersion)

	return nil
}

// updateRoutes updates route configuration
func (dcm *DynamicConfigManager) updateRoutes(update ConfigUpdate) error {
	switch update.Type {
	case ConfigUpdateTypeAdd:
		route, ok := update.Value.(RouteConfig)
		if !ok {
			return fmt.Errorf("invalid route configuration")
		}
		if err := dcm.validator.ValidateRouteConfig(route); err != nil {
			return err
		}
		dcm.config.Routes = append(dcm.config.Routes, route)

	case ConfigUpdateTypeUpdate:
		route, ok := update.Value.(RouteConfig)
		if !ok {
			return fmt.Errorf("invalid route configuration")
		}
		if err := dcm.validator.ValidateRouteConfig(route); err != nil {
			return err
		}

		// Find and update the route
		for i, r := range dcm.config.Routes {
			if r.Name == update.Key {
				dcm.config.Routes[i] = route
				return nil
			}
		}
		return fmt.Errorf("route %s not found", update.Key)

	case ConfigUpdateTypeDelete:
		// Remove the route
		for i, r := range dcm.config.Routes {
			if r.Name == update.Key {
				dcm.config.Routes = append(dcm.config.Routes[:i], dcm.config.Routes[i+1:]...)
				return nil
			}
		}
		return fmt.Errorf("route %s not found", update.Key)
	}

	return nil
}

// updateSecurity updates security configuration
func (dcm *DynamicConfigManager) updateSecurity(update ConfigUpdate) error {
	switch update.Key {
	case "rate_limit":
		rateLimitConfig, ok := update.Value.(RateLimitConfig)
		if !ok {
			return fmt.Errorf("invalid rate limit configuration")
		}
		dcm.config.Security.RateLimit = rateLimitConfig

	case "cors":
		corsConfig, ok := update.Value.(CORSConfig)
		if !ok {
			return fmt.Errorf("invalid CORS configuration")
		}
		dcm.config.Security.CORS = corsConfig

	case "waf":
		wafConfig, ok := update.Value.(WAFConfig)
		if !ok {
			return fmt.Errorf("invalid WAF configuration")
		}
		dcm.config.Security.WAF = wafConfig

	case "ip_filter":
		ipFilterConfig, ok := update.Value.(IPFilterConfig)
		if !ok {
			return fmt.Errorf("invalid IP filter configuration")
		}
		dcm.config.Security.IPFilter = ipFilterConfig

	default:
		return fmt.Errorf("unsupported security configuration key: %s", update.Key)
	}

	return dcm.validator.ValidateSecurityConfig(dcm.config.Security)
}

// updatePlugins updates plugin configuration
func (dcm *DynamicConfigManager) updatePlugins(update ConfigUpdate) error {
	switch update.Type {
	case ConfigUpdateTypeAdd, ConfigUpdateTypeUpdate:
		pluginConfig, ok := update.Value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("invalid plugin configuration")
		}

		if dcm.config.Plugins.Plugins == nil {
			dcm.config.Plugins.Plugins = make(map[string]interface{})
		}

		dcm.config.Plugins.Plugins[update.Key] = pluginConfig

	case ConfigUpdateTypeDelete:
		if dcm.config.Plugins.Plugins != nil {
			delete(dcm.config.Plugins.Plugins, update.Key)
		}
	}

	return dcm.validator.ValidatePluginConfig(dcm.config.Plugins)
}

// updateAuth updates authentication configuration
func (dcm *DynamicConfigManager) updateAuth(update ConfigUpdate) error {
	switch update.Key {
	case "jwt":
		jwtConfig, ok := update.Value.(JWTConfig)
		if !ok {
			return fmt.Errorf("invalid JWT configuration")
		}
		dcm.config.Auth.JWT = jwtConfig

	case "api_key":
		apiKeyConfig, ok := update.Value.(APIKeyConfig)
		if !ok {
			return fmt.Errorf("invalid API key configuration")
		}
		dcm.config.Auth.APIKey = apiKeyConfig

	case "oidc":
		oidcConfig, ok := update.Value.(OIDCConfig)
		if !ok {
			return fmt.Errorf("invalid OIDC configuration")
		}
		dcm.config.Auth.OIDC = oidcConfig

	default:
		return fmt.Errorf("unsupported auth configuration key: %s", update.Key)
	}

	return nil
}

// notifyListeners notifies all registered listeners about configuration changes
func (dcm *DynamicConfigManager) notifyListeners(ctx context.Context, change ConfigChange) {
	dcm.listenerMu.RLock()
	defer dcm.listenerMu.RUnlock()

	// Notify section-specific listeners
	if listeners, exists := dcm.listeners[change.Section]; exists {
		for _, listener := range listeners {
			go func(l ConfigChangeListener) {
				if err := l.OnConfigChange(ctx, change); err != nil {
					dcm.logger.Error("Listener failed to handle config change",
						"listener", l.GetName(),
						"error", err)
				}
			}(listener)
		}
	}

	// Notify global listeners
	if listeners, exists := dcm.listeners["*"]; exists {
		for _, listener := range listeners {
			go func(l ConfigChangeListener) {
				if err := l.OnConfigChange(ctx, change); err != nil {
					dcm.logger.Error("Global listener failed to handle config change",
						"listener", l.GetName(),
						"error", err)
				}
			}(listener)
		}
	}
}

// validateUpdate validates a configuration update
func (dcm *DynamicConfigManager) validateUpdate(update ConfigUpdate) error {
	if update.Section == "" {
		return fmt.Errorf("section is required")
	}

	if update.Type == ConfigUpdateTypeAdd || update.Type == ConfigUpdateTypeUpdate {
		if update.Value == nil {
			return fmt.Errorf("value is required for add/update operations")
		}
	}

	if update.Type == ConfigUpdateTypeUpdate || update.Type == ConfigUpdateTypeDelete {
		if update.Key == "" {
			return fmt.Errorf("key is required for update/delete operations")
		}
	}

	return nil
}

// saveConfigVersion saves a configuration version to history
func (dcm *DynamicConfigManager) saveConfigVersion(comment, source string) error {
	version := ConfigVersion{
		Version:   dcm.currentVersion,
		Config:    dcm.deepCopyConfig(dcm.config),
		Timestamp: time.Now(),
		Source:    source,
		Comment:   comment,
	}

	dcm.versionHistory = append(dcm.versionHistory, version)

	// Keep only the last 50 versions
	if len(dcm.versionHistory) > 50 {
		dcm.versionHistory = dcm.versionHistory[1:]
	}

	return nil
}

// persistConfig persists the current configuration to storage
func (dcm *DynamicConfigManager) persistConfig() error {
	if dcm.storage == nil {
		return nil
	}

	data, err := json.Marshal(dcm.config)
	if err != nil {
		return fmt.Errorf("failed to marshal configuration: %w", err)
	}

	return dcm.storage.Set("config", data)
}

// getConfigValue gets a configuration value by section and key
func (dcm *DynamicConfigManager) getConfigValue(config *Config, section, key string) interface{} {
	switch section {
	case "routes":
		for _, route := range config.Routes {
			if route.Name == key {
				return route
			}
		}
	case "security":
		switch key {
		case "rate_limit":
			return config.Security.RateLimit
		case "cors":
			return config.Security.CORS
		case "waf":
			return config.Security.WAF
		case "ip_filter":
			return config.Security.IPFilter
		}
	case "plugins":
		if config.Plugins.Plugins != nil {
			return config.Plugins.Plugins[key]
		}
	case "auth":
		switch key {
		case "jwt":
			return config.Auth.JWT
		case "api_key":
			return config.Auth.APIKey
		case "oidc":
			return config.Auth.OIDC
		}
	}
	return nil
}

// deepCopyConfig creates a deep copy of the configuration
func (dcm *DynamicConfigManager) deepCopyConfig(config *Config) *Config {
	data, err := json.Marshal(config)
	if err != nil {
		dcm.logger.Error("Failed to marshal config for deep copy", "error", err)
		return config
	}

	var copy Config
	if err := json.Unmarshal(data, &copy); err != nil {
		dcm.logger.Error("Failed to unmarshal config for deep copy", "error", err)
		return config
	}

	return &copy
}

// generateVersion generates a new version string
func generateVersion() string {
	return fmt.Sprintf("v%d", time.Now().Unix())
}
