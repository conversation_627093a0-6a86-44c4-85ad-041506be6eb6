package core

import (
	"context"
	"fmt"
	"net/http"
	"sync"

	"api-gateway/pkg/admin"
	"api-gateway/pkg/auth"
	"api-gateway/pkg/config"
	"api-gateway/pkg/discovery"
	"api-gateway/pkg/middleware"
	"api-gateway/pkg/plugin"
	"api-gateway/pkg/proxy"
	"api-gateway/pkg/security"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go"
)

// Gateway represents the main API gateway instance
type Gateway struct {
	config    *config.Config
	logger    *telemetry.Logger
	metrics   *telemetry.Metrics
	tracer    opentracing.Tracer

	// Core components
	authManager      *auth.Manager
	securityManager  *security.Manager
	discoveryManager *discovery.Manager
	proxyManager     *proxy.Manager
	pluginManager    *plugin.Manager

	// Dynamic configuration management
	configManager *config.DynamicConfigManager
	configStorage config.ConfigStorage

	// Admin API
	adminAPI *admin.ConfigAPI

	// HTTP router
	router *gin.Engine

	// Middleware chain
	middlewares []middleware.Middleware

	// Graceful shutdown
	shutdownFuncs []func() error
	mu            sync.RWMutex
}

// NewGateway creates a new gateway instance
func NewGateway(cfg *config.Config, logger *telemetry.Logger, metrics *telemetry.Metrics, tracer opentracing.Tracer) (*Gateway, error) {
	// Set Gin mode based on log level
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	gateway := &Gateway{
		config:        cfg,
		logger:        logger,
		metrics:       metrics,
		tracer:        tracer,
		shutdownFuncs: make([]func() error, 0),
	}

	// Initialize dynamic configuration management
	if err := gateway.initializeDynamicConfig(); err != nil {
		return nil, fmt.Errorf("failed to initialize dynamic configuration: %w", err)
	}

	// Initialize components
	if err := gateway.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize components: %w", err)
	}

	// Setup router and middleware
	if err := gateway.setupRouter(); err != nil {
		return nil, fmt.Errorf("failed to setup router: %w", err)
	}

	// Start metrics server
	if err := metrics.StartMetricsServer(cfg.Metrics); err != nil {
		return nil, fmt.Errorf("failed to start metrics server: %w", err)
	}

	return gateway, nil
}

// initializeDynamicConfig initializes dynamic configuration management
func (g *Gateway) initializeDynamicConfig() error {
	// Create configuration storage
	storageFactory := config.NewConfigStorageFactory(g.logger.With("component", "config-storage"))

	// Use memory storage by default, can be configured to use Redis or file storage
	storageConfig := map[string]interface{}{
		"type": "memory",
	}

	var err error
	g.configStorage, err = storageFactory.CreateStorage("memory", storageConfig)
	if err != nil {
		return fmt.Errorf("failed to create configuration storage: %w", err)
	}

	// Create dynamic configuration manager
	configLogger := telemetry.NewConfigLoggerAdapter(g.logger.With("component", "config-manager"))
	g.configManager = config.NewDynamicConfigManager(
		g.config,
		g.configStorage,
		configLogger,
	)

	// Start the configuration manager
	if err := g.configManager.Start(context.Background()); err != nil {
		return fmt.Errorf("failed to start configuration manager: %w", err)
	}

	// Create admin API
	adminLogger := telemetry.NewConfigLoggerAdapter(g.logger.With("component", "admin-api"))
	g.adminAPI = admin.NewConfigAPI(g.configManager, adminLogger, g.metrics)

	// Register configuration change listeners
	g.registerConfigListeners()

	return nil
}

// registerConfigListeners registers configuration change listeners
func (g *Gateway) registerConfigListeners() {
	// Route configuration listener
	routeListener := config.NewRouteConfigListener(
		"gateway-routes",
		g.logger.With("component", "route-listener"),
		g.handleRouteConfigChange,
	)
	g.configManager.AddListener("routes", routeListener)

	// Security configuration listener
	securityListener := config.NewSecurityConfigListener(
		"gateway-security",
		g.logger.With("component", "security-listener"),
		g.handleSecurityConfigChange,
	)
	g.configManager.AddListener("security", securityListener)

	// Plugin configuration listener
	pluginListener := config.NewPluginConfigListener(
		"gateway-plugins",
		g.logger.With("component", "plugin-listener"),
		g.handlePluginConfigChange,
	)
	g.configManager.AddListener("plugins", pluginListener)

	// Auth configuration listener
	authListener := config.NewAuthConfigListener(
		"gateway-auth",
		g.logger.With("component", "auth-listener"),
		g.handleAuthConfigChange,
	)
	g.configManager.AddListener("auth", authListener)
}

// initializeComponents initializes all gateway components
func (g *Gateway) initializeComponents() error {
	var err error

	// Initialize authentication manager
	g.authManager, err = auth.NewManager(g.config.Auth, g.logger.With("component", "auth"))
	if err != nil {
		return fmt.Errorf("failed to initialize auth manager: %w", err)
	}

	// Initialize security manager
	g.securityManager, err = security.NewManager(g.config.Security, g.logger.With("component", "security"), g.metrics)
	if err != nil {
		return fmt.Errorf("failed to initialize security manager: %w", err)
	}

	// Initialize service discovery manager
	g.discoveryManager, err = discovery.NewManager(g.config.Discovery, g.logger.With("component", "discovery"))
	if err != nil {
		return fmt.Errorf("failed to initialize discovery manager: %w", err)
	}

	// Initialize proxy manager
	g.proxyManager, err = proxy.NewManager(g.config, g.logger.With("component", "proxy"), g.metrics, g.discoveryManager)
	if err != nil {
		return fmt.Errorf("failed to initialize proxy manager: %w", err)
	}

	// Initialize plugin manager
	g.pluginManager, err = plugin.NewManager(g.config.Plugins, g.logger.With("component", "plugin"), g.metrics)
	if err != nil {
		return fmt.Errorf("failed to initialize plugin manager: %w", err)
	}

	return nil
}

// setupRouter sets up the HTTP router and middleware chain
func (g *Gateway) setupRouter() error {
	g.router = gin.New()

	// Add core middleware
	g.addCoreMiddleware()

	// Add security middleware
	g.addSecurityMiddleware()

	// Add authentication middleware
	g.addAuthMiddleware()

	// Add plugin middleware
	g.addPluginMiddleware()

	// Setup routes
	g.setupRoutes()

	return nil
}

// addCoreMiddleware adds core middleware to the router
func (g *Gateway) addCoreMiddleware() {
	// Request ID middleware
	g.router.Use(middleware.RequestID())

	// Logging middleware
	g.router.Use(middleware.Logger(g.logger.RequestLogger()))

	// Recovery middleware
	g.router.Use(middleware.Recovery(g.logger))

	// Metrics middleware
	g.router.Use(middleware.Metrics(g.metrics))

	// Tracing middleware
	g.router.Use(middleware.Tracing(g.tracer))

	// Connection tracking middleware
	g.router.Use(middleware.ConnectionTracker(g.metrics))
}

// addSecurityMiddleware adds security middleware to the router
func (g *Gateway) addSecurityMiddleware() {
	// CORS middleware
	if g.config.Security.CORS.Enabled {
		g.router.Use(middleware.CORS(g.config.Security.CORS))
	}

	// IP filter middleware
	if g.config.Security.IPFilter.Enabled {
		g.router.Use(middleware.IPFilter(g.config.Security.IPFilter, g.logger.SecurityLogger()))
	}

	// WAF middleware
	if g.config.Security.WAF.Enabled {
		g.router.Use(middleware.WAF(g.config.Security.WAF, g.logger.SecurityLogger(), g.metrics))
	}

	// Rate limiting middleware
	if g.config.Security.RateLimit.Enabled {
		g.router.Use(middleware.RateLimit(g.config.Security.RateLimit, g.logger.SecurityLogger(), g.metrics))
	}
}

// addAuthMiddleware adds authentication middleware to the router
func (g *Gateway) addAuthMiddleware() {
	// Authentication middleware (applied selectively based on route configuration)
	g.router.Use(middleware.Authentication(g.authManager, g.logger.SecurityLogger(), g.metrics))
}

// addPluginMiddleware adds plugin middleware to the router
func (g *Gateway) addPluginMiddleware() {
	// Plugin execution middleware
	g.router.Use(middleware.PluginExecution(g.pluginManager, g.logger, g.metrics))
}

// setupRoutes sets up the gateway routes
func (g *Gateway) setupRoutes() {
	// Health check endpoint
	g.router.GET("/health", g.healthCheck)

	// Admin API endpoints
	admin := g.router.Group("/admin")
	{
		// Legacy endpoints for backward compatibility
		admin.GET("/config", g.getConfig)
		admin.POST("/config/reload", g.reloadConfig)
		admin.GET("/routes", g.getRoutes)
		admin.GET("/plugins", g.getPlugins)
		admin.GET("/metrics", gin.WrapH(g.metrics.Handler()))

		// New dynamic configuration API
		apiGroup := admin.Group("/api/v1")
		g.adminAPI.RegisterRoutes(apiGroup)
	}

	// Setup proxy routes
	for _, route := range g.config.Routes {
		g.setupProxyRoute(route)
	}

	// Catch-all route for dynamic routing
	g.router.NoRoute(g.handleDynamicRoute)
}

// setupProxyRoute sets up a proxy route
func (g *Gateway) setupProxyRoute(route config.RouteConfig) {
	handler := g.proxyManager.CreateHandler(route)
	
	if route.Method == "*" {
		g.router.Any(route.Path, handler)
	} else {
		g.router.Handle(route.Method, route.Path, handler)
	}
}

// handleDynamicRoute handles dynamic routing for unmatched routes
func (g *Gateway) handleDynamicRoute(c *gin.Context) {
	// Try to find a matching route through service discovery
	handler := g.proxyManager.CreateDynamicHandler(c.Request.URL.Path, c.Request.Method)
	if handler != nil {
		handler(c)
		return
	}

	// No route found
	c.JSON(http.StatusNotFound, gin.H{
		"error":   "route not found",
		"path":    c.Request.URL.Path,
		"method":  c.Request.Method,
		"message": "The requested route is not available",
	})
}

// healthCheck handles health check requests
func (g *Gateway) healthCheck(c *gin.Context) {
	status := gin.H{
		"status":  "healthy",
		"version": "1.0.0",
		"uptime":  "unknown", // TODO: implement uptime tracking
	}

	// Check component health
	components := gin.H{}
	
	if g.authManager != nil {
		components["auth"] = "healthy"
	}
	
	if g.securityManager != nil {
		components["security"] = "healthy"
	}
	
	if g.discoveryManager != nil {
		components["discovery"] = g.discoveryManager.HealthStatus()
	}
	
	if g.proxyManager != nil {
		components["proxy"] = "healthy"
	}
	
	if g.pluginManager != nil {
		components["plugins"] = "healthy"
	}

	status["components"] = components

	c.JSON(http.StatusOK, status)
}

// getConfig returns the current configuration
func (g *Gateway) getConfig(c *gin.Context) {
	c.JSON(http.StatusOK, g.config)
}

// reloadConfig reloads the gateway configuration
func (g *Gateway) reloadConfig(c *gin.Context) {
	// TODO: implement configuration reload
	c.JSON(http.StatusOK, gin.H{
		"message": "Configuration reload not implemented yet",
	})
}

// getRoutes returns the current routes
func (g *Gateway) getRoutes(c *gin.Context) {
	c.JSON(http.StatusOK, g.config.Routes)
}

// getPlugins returns the current plugins
func (g *Gateway) getPlugins(c *gin.Context) {
	plugins := g.pluginManager.GetLoadedPlugins()
	c.JSON(http.StatusOK, plugins)
}

// Handler returns the HTTP handler for the gateway
func (g *Gateway) Handler() http.Handler {
	return g.router
}

// Shutdown gracefully shuts down the gateway
func (g *Gateway) Shutdown(ctx context.Context) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.logger.Info("Shutting down gateway components...")

	// Stop dynamic configuration manager
	if g.configManager != nil {
		if err := g.configManager.Stop(); err != nil {
			g.logger.Error("Error stopping configuration manager", "error", err)
		}
	}

	// Close configuration storage
	if g.configStorage != nil {
		if err := g.configStorage.Close(); err != nil {
			g.logger.Error("Error closing configuration storage", "error", err)
		}
	}

	// Execute shutdown functions in reverse order
	for i := len(g.shutdownFuncs) - 1; i >= 0; i-- {
		if err := g.shutdownFuncs[i](); err != nil {
			g.logger.Error("Error during component shutdown", "error", err)
		}
	}

	return nil
}

// AddShutdownFunc adds a function to be called during shutdown
func (g *Gateway) AddShutdownFunc(fn func() error) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.shutdownFuncs = append(g.shutdownFuncs, fn)
}

// Configuration change handlers

// handleRouteConfigChange handles route configuration changes
func (g *Gateway) handleRouteConfigChange(ctx context.Context, change config.ConfigChange) error {
	g.logger.Info("Handling route configuration change",
		"type", change.Type,
		"key", change.Key)

	// Get the updated configuration
	updatedConfig := g.configManager.GetConfig()

	// Update the gateway's configuration reference
	g.mu.Lock()
	g.config = updatedConfig
	g.mu.Unlock()

	// Recreate proxy handlers for the affected routes
	// This is a simplified approach - in production, you might want more granular updates
	if err := g.updateProxyRoutes(); err != nil {
		return fmt.Errorf("failed to update proxy routes: %w", err)
	}

	g.logger.Info("Route configuration change applied successfully")
	return nil
}

// handleSecurityConfigChange handles security configuration changes
func (g *Gateway) handleSecurityConfigChange(ctx context.Context, change config.ConfigChange) error {
	g.logger.Info("Handling security configuration change",
		"type", change.Type,
		"key", change.Key)

	// Get the updated configuration
	updatedConfig := g.configManager.GetConfig()

	// Update the gateway's configuration reference
	g.mu.Lock()
	g.config = updatedConfig
	g.mu.Unlock()

	// Update security manager with new configuration
	if g.securityManager != nil {
		if err := g.securityManager.UpdateConfig(updatedConfig.Security); err != nil {
			return fmt.Errorf("failed to update security manager: %w", err)
		}
	}

	g.logger.Info("Security configuration change applied successfully")
	return nil
}

// handlePluginConfigChange handles plugin configuration changes
func (g *Gateway) handlePluginConfigChange(ctx context.Context, change config.ConfigChange) error {
	g.logger.Info("Handling plugin configuration change",
		"type", change.Type,
		"key", change.Key)

	// Get the updated configuration
	updatedConfig := g.configManager.GetConfig()

	// Update the gateway's configuration reference
	g.mu.Lock()
	g.config = updatedConfig
	g.mu.Unlock()

	// Update plugin manager with new configuration
	if g.pluginManager != nil {
		if err := g.pluginManager.UpdateConfig(updatedConfig.Plugins); err != nil {
			return fmt.Errorf("failed to update plugin manager: %w", err)
		}
	}

	g.logger.Info("Plugin configuration change applied successfully")
	return nil
}

// handleAuthConfigChange handles authentication configuration changes
func (g *Gateway) handleAuthConfigChange(ctx context.Context, change config.ConfigChange) error {
	g.logger.Info("Handling auth configuration change",
		"type", change.Type,
		"key", change.Key)

	// Get the updated configuration
	updatedConfig := g.configManager.GetConfig()

	// Update the gateway's configuration reference
	g.mu.Lock()
	g.config = updatedConfig
	g.mu.Unlock()

	// Update auth manager with new configuration
	if g.authManager != nil {
		if err := g.authManager.UpdateConfig(updatedConfig.Auth); err != nil {
			return fmt.Errorf("failed to update auth manager: %w", err)
		}
	}

	g.logger.Info("Auth configuration change applied successfully")
	return nil
}

// updateProxyRoutes updates proxy routes based on current configuration
func (g *Gateway) updateProxyRoutes() error {
	// This is a simplified implementation
	// In production, you might want to update routes more granularly

	// Remove existing dynamic routes (keep static routes like health, admin)
	// This would require more sophisticated route management

	// Re-setup proxy routes
	for _, route := range g.config.Routes {
		g.setupProxyRoute(route)
	}

	return nil
}
